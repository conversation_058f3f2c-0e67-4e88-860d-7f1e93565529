import { useCallback, useMemo } from 'react';
import { message, Tree } from 'antd';
import { isEmpty } from 'lodash';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import CaseTitle from './components/CaseTitle';
import styles from './DirectoryList.module.less';

const { DirectoryTree } = Tree;

function DirectoryList(props) {
    const { currentSpace, directoryList } = props;
    const query = getQueryParams();
    const navigate = useNavigate();
    const selectKey = +query?.planId;

    const getDirectotyTree = (data) => {
        if (!data) {
            return;
        }
        return data.map((item) => ({
            ...item,
            key: item.planId
        }));
    };

    // 获取目录树
    const directoryTree = useMemo(() => {
        return getDirectotyTree(directoryList);
    }, [directoryList]);

    // 切换
    const handleClickDirectoryTreeNode = (value, node) => {
        if (isEmpty(value)) {
            message.warning('请重新选择');
            return;
        }
        navigate(
            stringifyUrl({
                url: '/lazyperf/detail',
                query: {
                    planId: node?.planId,
                    moduleId: currentSpace?.id,
                    planType: node?.planType,
                    stage: query?.stage ?? 'task'
                }
            })
        );
    };

    return (
        <DirectoryTree
            icon={false}
            switcherIcon={false}
            treeData={directoryTree}
            selectedKeys={[selectKey]}
            onSelect={(value, e) => {
                const { selectedNodes } = e;
                handleClickDirectoryTreeNode(value, selectedNodes[0]);
            }}
            titleRender={(node) => {
                return <CaseTitle node={node} selectKey={selectKey} />;
            }}
        />
    );
}

export default connectModel([baseModel], (state) => ({
    currentModule: state.common.base.currentModule,
    currentSpace: state.common.base.currentSpace
}))(DirectoryList);
