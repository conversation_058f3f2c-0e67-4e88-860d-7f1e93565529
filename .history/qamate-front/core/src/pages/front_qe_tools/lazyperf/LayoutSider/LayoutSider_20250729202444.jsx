import { useState, useEffect, useCallback } from 'react';
import { isEmpty } from 'lodash';
import { Tooltip, Spin, Badge, Pagination } from 'antd';
import classnames from 'classnames';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import { SearchOutlined, FolderTwoTone, PlusOutlined } from '@ant-design/icons';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import lazyperfModel from 'COMMON/models/lazyperfModel';
import { getPerfPlanList } from 'COMMON/api/front_qe_tools/lazyperf';
import { getQueryParams } from 'COMMON/utils/utils';
import NoContent from 'COMMON/components/common/NoContent';
import Search from 'COMMON/components/Search';
import DirectoryList from './components/DirectoryList';
import styles from './LayoutSider.module.less';

function LayoutSider(props) {
    const { currentSpace, perfPlanList, setPerfPlanList, currentModule } = props;
    const [loading, setLoading] = useState(false);
    const [showSearch, setShowSearch] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const [originalPerfPlanList, setOriginalPerfPlanList] = useState([]);
    const [filteredPerfPlanList, setFilteredPerfPlanList] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [expandedParent, setExpandedParent] = useState(false);
    // 分页状态
    const [pagination, setPagination] = useState({
        pageIndex: 1,
        pageSize: 20
    });
    const [total, setTotal] = useState(0);
    const navigate = useNavigate();
    const query = getQueryParams();

    const handleSearchClick = (value) => {
        setSearchValue(value);
    };

    const handleSearchBlur = () => {
        setShowSearch(false);
        // 如果搜索值为空，则清除搜索状态
        if (!searchValue || searchValue.trim() === '') {
            setSearchValue('');
        }
    };

    const refreshPerfPlanList = useCallback(
        async (
            moduleId = currentSpace?.id,
            pageIndex = pagination.pageIndex,
            pageSize = pagination.pageSize
        ) => {
            try {
                setLoading(true);
                const response = await getPerfPlanList({
                    moduleId: moduleId,
                    pageIndex: pageIndex,
                    pageSize: pageSize
                });
                const { planList, total: totalCount } = response;
                setPerfPlanList(planList);
                setOriginalPerfPlanList(planList);
                setTotal(totalCount || 0);
            } catch (error) {
                console.error('获取性能评测计划列表失败:', error);
            } finally {
                setLoading(false);
            }
        },
        [currentSpace?.id, setPerfPlanList, pagination.pageIndex, pagination.pageSize]
    );

    // 分页变化处理函数
    const handlePaginationChange = useCallback((pageIndex, pageSize) => {
        setPagination({
            pageIndex,
            pageSize
        });
    }, []);

    // EventBus刷新处理函数 - 重置到第一页
    const handleEventBusRefresh = useCallback(() => {
        setPagination({
            pageIndex: 1,
            pageSize: pagination.pageSize
        });
        refreshPerfPlanList(currentSpace?.id, 1, pagination.pageSize);
    }, [currentSpace?.id, refreshPerfPlanList, pagination.pageSize]);

    // 获取目录树
    useEffect(() => {
        if (!currentSpace?.id) {
            return;
        }
        refreshPerfPlanList(currentSpace.id, pagination.pageIndex, pagination.pageSize);
        EventBus.on('refreshPerfPlanList', handleEventBusRefresh);

        return () => {
            EventBus.off('refreshPerfPlanList', handleEventBusRefresh);
        };
    }, [currentSpace?.id, refreshPerfPlanList, pagination.pageIndex, pagination.pageSize, handleEventBusRefresh]);

    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            if (currentSpace?.id !== +query?.moduleId) {
                navigate(
                    stringifyUrl({
                        url: '/lazyperf/index',
                        query: {
                            moduleId: currentSpace?.id
                        }
                    })
                );
            }
            // setCurTreeNodeId(treeNodeId);
            // refreshMrdList(treeNodeId);
        }
        func();
    }, [currentSpace?.id]);

    // 同步数据
    useEffect(() => {
        if (perfPlanList && perfPlanList !== originalPerfPlanList) {
            setOriginalPerfPlanList(perfPlanList);
        }
    }, [perfPlanList, originalPerfPlanList]);

    // 搜索
    useEffect(() => {
        if (isEmpty(originalPerfPlanList)) {
            setFilteredPerfPlanList([]);
            return;
        }

        if (!searchValue || searchValue.trim() === '') {
            // 没有搜索词时显示所有数据
            setFilteredPerfPlanList(originalPerfPlanList);
        } else {
            // 根据搜索词过滤数据
            const filtered = originalPerfPlanList.filter((item) =>
                item?.name?.includes(searchValue)
            );
            setFilteredPerfPlanList(filtered);
        }
    }, [searchValue, originalPerfPlanList]);

    // 获取要显示的列表数据
    const displayPerfPlanList =
        filteredPerfPlanList.length > 0 || searchValue ? filteredPerfPlanList : perfPlanList;

    return (
        <>
            <div className={styles.layoutSider}>
                <div className={styles.siderHeader}>
                    {!showSearch && (
                        <div className={styles.groupInfo}>
                            <FolderTwoTone twoToneColor="#777" />
                            <span className={styles.text}>计划列表</span>
                        </div>
                    )}
                    {/* 搜索框 */}
                    {showSearch && (
                        <Search
                            className={styles.searchStyle}
                            value={searchValue}
                            onChange={setSearchValue}
                            onSearch={handleSearchClick}
                            onBlur={handleSearchBlur}
                            searchLabel="搜索计划名称"
                        />
                    )}
                    <div className={styles.iconGroup}>
                        {/* 搜索icon */}
                        {!showSearch && (
                            <Tooltip title="搜索">
                                <div
                                    className={classnames(styles.iconWrapper)}
                                    onClick={() => {
                                        setShowSearch(true);
                                    }}
                                >
                                    <Badge dot={searchValue !== ''}>
                                        <SearchOutlined className={styles.addIcon} />
                                    </Badge>
                                </div>
                            </Tooltip>
                        )}
                        {/* 新建性能评测计划icon */}
                        <div className={classnames(styles.iconWrapper)}>
                            <span className={styles.addIcon}>
                                <PlusOutlined
                                    onClick={() => {
                                        navigate(
                                            stringifyUrl({
                                                url: '/' + currentModule + '/create',
                                                query: {
                                                    moduleId: currentSpace?.id
                                                }
                                            })
                                        );
                                    }}
                                />
                            </span>
                        </div>
                    </div>
                </div>
                <Spin spinning={loading}>
                    {isEmpty(displayPerfPlanList) ? (
                        <NoContent
                            text={searchValue ? '未找到匹配的计划' : '暂无计划'}
                            className={styles.noContent}
                        />
                    ) : (
                        <>
                            <DirectoryList
                                directoryList={displayPerfPlanList}
                                expandedKeys={expandedKeys}
                                expandedParent={expandedParent}
                                setExpandedParent={setExpandedParent}
                                setExpandedKeys={setExpandedKeys}
                            />
                            {/* 分页组件 - 只在非搜索状态下显示 */}
                            {!searchValue 
                            // && total > pagination.pageSize
                              && (
                                <div className={styles.paginationWrapper}>
                                    <Pagination
                                        simple
                                        size="small"
                                        current={pagination.pageIndex}
                                        total={total}
                                        pageSize={pagination.pageSize}
                                        showSizeChanger={false}
                                        onChange={handlePaginationChange}
                                    />
                                </div>
                            )}
                        </>
                    )}
                </Spin>
            </div>
        </>
    );
}

export default connectModel([baseModel, lazyperfModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    perfPlanList: state.common.lazyperf.perfPlanList
}))(LayoutSider);
