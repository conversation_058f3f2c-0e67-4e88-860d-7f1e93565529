@import "RESOURCES/css/common.less";

.noContent {
    margin-top: 200px;
}

.layoutSider {
    position: relative;
    height: calc(100vh - 40px);
    background-color: var(--layout-background-color);
    background-image: linear-gradient(180deg, var(--layout-background-color), #fefeff);
    z-index: 99;
    overflow: hidden;

    :global {

        .ant-tree,
        .ant-menu {
            background-color: transparent;
        }

        .ant-tree-switcher {
            width: 0px;
            line-height: 30px !important;
        }

        .ant-tree-treenode {
            padding: 0 10px 5px 0;
        }

        .ant-tree .ant-tree-treenode-draggable {
            cursor: pointer !important;
        }

    }
}

.siderHeader {
    height: 30px;
    line-height: 30px;
    margin-bottom: 5px;

    .groupInfo {
        float: left;
        width: calc(100% - 55px);
        overflow: hidden;
    }

    .searchStyle {
        width: calc(100% - 50px) !important;
        margin: 5px 0 0 8px;
    }

    .iconGroup {
        float: right;
        margin: 3px 13px 0 0;
    }

    .addIcon {
        text-align: center;
        font-size: 14px;
        color: var(--color2);
    }
}

.iconWrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.iconWrapper:hover {
    background-color: rgba(0, 0, 0, 0.1);
    /* 你可以根据需要调整颜色 */
}

// 新增
.addCaseText {
    font-size: 12px;
}

.btnPos {
    float: right;
}

.operatorIcon {
    color: var(--color2);
    padding-right: 8px;
    font-size: 14px;
}

.delIcon {
    color: var(--error-color);
    padding-right: 8px;
    font-size: 12px;
}

.flexCenterX {
    display: flex;
    align-items: center;
    font-size: 12px;
}

.selectOption {
    margin-bottom: 5px;
}

.title {
    display: inline-block;
    width: 80px;
    font-size: 14px;
}

.groupInfo {
    padding-left: 12px;
    font-size: 12px;
    font-weight: bold;
    cursor: default;

    .text {
        margin-left: 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}