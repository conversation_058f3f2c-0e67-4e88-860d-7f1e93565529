import { useEffect, useRef, useState } from 'react';
import { Outlet, useLocation } from 'umi';
import moment from 'moment';
import { isEmpty } from 'lodash';
import { useNavigate } from 'umi';
import 'moment/locale/zh-cn';
import { Layout, message, notification } from 'antd';
import { ExclamationCircleTwoTone, CloseCircleTwoTone } from '@ant-design/icons';
import createKuSDK from '@baidu/ku-mega-sdk'; // 引入知识库 ku-mega-sdk 最新版本
import classnames from 'classnames';
import { stringifyUrl } from 'query-string';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import newBaseModel from 'COMMON/models/newBaseModel';
import { getQueryParams, delay } from 'COMMON/utils/utils';
import { post } from 'COMMON/utils/requestUtils';
import electron from 'COMMON/utils/electron';
import { loadWhitelistFromBOS } from 'COMMON/config/whiteList';
import { versionCmp } from './utils';
import {
    InitBoard,
    UpdateBoard,
    ErrorNotice,
    ModuleUpdateModal,
    FeUpdateModal,
    ElectronUpdateModal
} from './components';
import styles from './BasicLayout.module.less';

moment.locale('zh-cn');

const pathModuleList = [
    'trd',
    'iteration-case',
    'regression-case',
    'inspection-case',
    'device-manage',
    'testone',
    'integration',
    'system-setting',
    'laboratory',
    'iteration',
    'case',
    'plan',
    'workshop',
    'device',
    'proxy-manage',
    'intelligent',
    'apicode'
];

function BasicLayout(props) {
    const {
        className,
        spaceList,
        getSpaceList,
        currentSpace,
        setWholePath,
        currentModule,
        setCurrentModule,
        version,
        token,
        getToken,
        queryFinalAuth,
        userAccess,
        setKuSDK,
        throwMsg,
        throwNotification,
        setThrowNotification,
        statusCode,
        setStatusCode,
        setAsider
    } = props;
    // 首次工具加载判断
    const [init, setInit] = useState(false);
    // 热更新进度
    const [updatePercent, setUpdatePercent] = useState(0);
    // 工具是否存在第一次提示需要进行热更新
    const [updateInit, setUpdateInit] = useState(true);

    const query = getQueryParams();
    const location = useLocation();
    const navigate = useNavigate();
    const feUpdateModalRef = useRef();
    const electronUpdateModalRef = useRef();
    const [notificationApi, notificationContextHolder] = notification.useNotification();
    const [messageApi, contextHolder] = message.useMessage();

    // error 异常信息提示
    useEffect(() => {
        if (throwNotification === null) {
            return;
        }
        console.log('Request Id:' + throwNotification?.requestId);
        let params = {
            duration: 5,
            showProgress: true
        };
        params.description = <ErrorNotice throwNotification={throwNotification} />;
        notificationApi.open(params);
    }, [throwNotification]);

    // error 拦截信息提示
    useEffect(() => {
        if (throwMsg === null) {
            return;
        }
        messageApi.open({
            ...throwMsg,
            type: throwMsg?.type ?? 'error',
            content: throwMsg?.message ? throwMsg?.message + '' : undefined
        });
    }, [throwMsg]);

    // 工具ready全局监听
    useEffect(() => {
        const getReady = async () => {
            while (true) {
                let res = await electron.send('system.isAppReady');
                setStatusCode(res);
                if (4 === res) {
                    break;
                } else if (5 === res) {
                    break;
                } else if (6 === res) {
                    break;
                } else if (1 === res) {
                    break;
                }
                await delay(1000);
            }
        };
        if (!init && isElectron()) {
            electron.send('ready');
            setInit(true);
            getReady();
        }
    }, []);

    // 工具热更新进度监听
    useEffect(() => {
        const handler = ({ percent }) => {
            setUpdatePercent(percent);
        };
        if (4 === statusCode && isElectron()) {
            electron.on('update.percent', handler);
            return () => electron.remove('update.percent', handler);
        }
    }, [statusCode]);

    useEffect(() => {
        setAsider(query?.hasSider === 'false' ? false : true);
    }, [query?.hasSider]);

    useEffect(() => {
        const matched = pathModuleList.find((key) => location.pathname.includes(key));
        if (matched) {
            setCurrentModule(matched);
        }
    }, [location.pathname]);

    // 权限获取
    useEffect(() => {
        async function func1() {
            try {
                await queryFinalAuth({ productId: currentSpace.id });
                let flag = false;
                for (let _space of spaceList) {
                    if (_space?.children?.map((item) => item.id)?.includes(currentSpace.id)) {
                        flag = true;
                    }
                }
                setWholePath({});
                if (!flag) {
                    message.warning('无权限访问该业务线');
                    navigate(
                        stringifyUrl({
                            url: '/case/index'
                        })
                    );
                    return;
                }
            } catch (err) {}
        }
        if (currentSpace && isEmpty(userAccess)) {
            func1();
        }
    }, [currentSpace]);

    // 工具是否存在热更新推送
    useEffect(() => {
        const handler = () => {
            // 第一次推送给出弹窗提示
            if (updateInit) {
                setThrowNotification({
                    msg: '有新版本啦，来重启工具进行更新！给你新的体验～',
                    duration: null
                });
                setUpdateInit(false);
            }
        };
        if (isElectron()) {
            electron.on('hotupdate.available', handler);
            return () => electron.remove('hotupdate.available', handler);
        }
    });

    // 工具是否存在热更新推送
    useEffect(() => {
        const handler = () => {
            setStatusCode(6);
        };
        if (isElectron()) {
            electron.on('system.serverReadyToUpdate', handler);
            return () => electron.remove('system.serverReadyToUpdate', handler);
        }
    });

    // 全局监听
    useEffect(() => {
        // 处理通知逻辑
        const handler = ({ title, desc, detail }) => {
            let description = desc;
            if (detail) {
                description += `\n${detail}`;
            }
            setThrowNotification({
                msg: title,
                description,
                duration: 4.5,
                icon: <ExclamationCircleTwoTone />,
                style: {
                    whiteSpace: 'pre-wrap'
                }
            });
        };
        if (isElectron()) {
            electron.on('notification.warn', handler);
            return () => electron.remove('notification.warn', handler);
        }
    }, []);

    useEffect(() => {
        const handler = ({ title, desc, detail }) => {
            let description = desc;
            if (detail) {
                description += `\n${detail}`;
            }
            setThrowNotification({
                msg: title,
                description,
                style: {
                    whiteSpace: 'pre-wrap'
                },
                duration: null,
                icon: <CloseCircleTwoTone twoToneColor="#ff4d4f" />
            });
        };
        if (isElectron()) {
            electron.on('notification.error', handler);
            return () => electron.remove('notification.error', handler);
        }
    }, []);

    // 创建知识库sdk
    const getKuSDK = async () => {
        try {
            const sdk = await createKuSDK({
                env: MEGA_SDK_ENV,
                appId: MEGA_SDK_APPID,
                onTokenUpdate: async () => {
                    let { token } = await post('/core/user/ku/token');
                    return token;
                }
            });
            if (!sdk) {
                message.error('知识库 SDK 加载失败');
                return;
            }
            setKuSDK(sdk);
        } catch (err) {
            message.error('知识库 SDK 获取失败：未知异常');
        }
    };

    const func = async () => {
        let import_moduleId = query?.spaceId ?? query?.productId ?? query?.moduleId;
        if (import_moduleId) {
            localStorage.setItem('spaceId', import_moduleId);
        }
        try {
            await getToken();
            try {
                try {
                    await loadWhitelistFromBOS();
                } catch (err) {}
                await getKuSDK();
            } catch (err) {}
            await getSpaceList();
        } catch (err) {}
    };

    // web 加载时直接获取
    useEffect(() => {
        if (!isElectron()) {
            console.log(' web 加载时直接获取');
            func();
        }
    }, []);

    // electron 加载时直接获取
    useEffect(() => {
        if (statusCode === 1 && isElectron()) {
            console.log('electron 加载时直接获取');
            func();
        }
    }, [statusCode]);

    useEffect(() => {
        const func1 = async () => {
            try {
                if (currentSpace?.id) {
                    if (isElectron()) {
                        await electron.send('system.space.checkoutSpace', {
                            spaceId: currentSpace?.id
                        });
                    }
                }
            } catch (err) {}
        };
        func1();
    }, [currentSpace]);

    // 页面刷新检测
    useEffect(() => {
        // 开发环境不更新
        if (process.env.NODE_ENV === 'development' || window.location.href.includes('http:')) {
            return;
        }
        let timer = setInterval(() => {
            // 非工具 或 工具无需热更新
            if ((isElectron() && 4 !== statusCode) || !isElectron()) {
                post('/common/version/latest').then((res) => {
                    let feUpdateVersion = res.versionInfo.fe.version
                        .split('.')
                        .map((item) => Number(item));
                    let feCurVersion = version?.fe.split('.').map((item) => Number(item));
                    if (isElectron()) {
                        // 工具环境
                        electron.send('system.getSystemVersion').then((_res) => {
                            let ideUpdateVersion = res.versionInfo.ide.version
                                .split('.')
                                .map((item) => Number(item));
                            let ideCurVersion = _res.split('.').map((item) => Number(item));
                            if (versionCmp(ideUpdateVersion, ideCurVersion)) {
                                console.log(
                                    '检测到工具版本更新: 由' +
                                        _res +
                                        '更新为' +
                                        res.versionInfo.ide.version
                                );
                                electronUpdateModalRef.current?.show();
                                clearInterval(timer);
                            } else if (isElectron() && versionCmp(feUpdateVersion, feCurVersion)) {
                                console.log(
                                    '检测到工具的前端版本更新: 由' +
                                        version?.fe +
                                        '更新为' +
                                        res.versionInfo.fe.version
                                );
                                feUpdateModalRef.current?.show('electron');
                                clearInterval(timer);
                            }
                        });
                    } else if (versionCmp(feUpdateVersion, feCurVersion)) {
                        // web 环境
                        console.log(
                            '检测到前端版本更新: 由' +
                                version?.fe +
                                '更新为' +
                                res.versionInfo.fe.version
                        );
                        feUpdateModalRef.current?.show('web');
                        clearInterval(timer);
                    }
                });
            }
        }, 30000);
        return () => clearInterval(timer);
    }, []);

    // 工具端初始化
    if (isElectron()) {
        const componentMap = {
            // 初始化
            0: (
                <InitBoard text="初始化，首次加载耗时约 1 分钟。我们在努力搬砖中，请耐心等待一下下" />
            ),
            // 工具热更新
            4: <UpdateBoard updatePercent={updatePercent} />,
            // 工具策略下载中
            5: <InitBoard text="策略下载中，请耐心等待" />,
            // 工具热更新
            6: <ModuleUpdateModal apiUrl="update.front.updateServerVersion" />
        };
        const Component = componentMap[statusCode];
        if (Component) return Component;
    }

    // 无token
    if (!token) {
        console.log('获取登录信息中');
        return <InitBoard text="获取登录信息中" />;
    }

    // 登陆后,无业务线权限
    if (token && isEmpty(spaceList)) {
        return <InitBoard text="暂无任何业务线权限，请联系业务同学或者QAMate平台同学添加" />;
    }

    return (
        <div>
            {contextHolder}
            {notificationContextHolder}
            <Layout className={classnames(className, styles.layoutContainer)}>
                <Layout className={styles.right}>
                    <Outlet key={currentModule + currentSpace?.id} />
                </Layout>
                <FeUpdateModal ref={feUpdateModalRef} />
                <ElectronUpdateModal ref={electronUpdateModalRef} />
            </Layout>
        </div>
    );
}

export default connectModel([baseModel, newBaseModel], (state) => ({
    token: state.common.base.token,
    asider: state.common.base.asider,
    kuSDK: state.common.base.kuSDK,
    statusCode: state.common.new_base.statusCode,
    throwMsg: state.common.base.throwMsg,
    throwNotification: state.common.base.throwNotification,
    version: state.common.base.version,
    spaceList: state.common.base.spaceList,
    wholePath: state.common.base.wholePath,
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    userAccess: state.common.base.userAccess
}))(BasicLayout);
