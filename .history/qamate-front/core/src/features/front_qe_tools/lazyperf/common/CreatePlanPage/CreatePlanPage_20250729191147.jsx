import { useEffect, useState } from 'react';
import { useNavigate } from 'umi';
import { Button, Card, Descriptions, Form, Input, message, Radio, Spin, Select, Tag } from 'antd';
import { isEmpty } from 'lodash';
import { stringifyUrl } from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import commonModel from 'COMMON/models/commonModel';
import { CardContent } from 'COMMON/components/common/Card';
import { CardHeader } from 'COMMON/components/common/Card';
import { CardTitle } from 'COMMON/components/common/Card';
import { getEnvList } from 'COMMON/api/front_qe_tools/config';
import { getTreeNodeList } from 'COMMON/api/front_qe_tools/tree';
import { getGroupList } from 'COMMON/api/front_qe_tools/group';
import { getCaseTree } from 'COMMON/api/front_qe_tools/node';
import { createPerfPlanList } from 'COMMON/api/front_qe_tools/lazyperf';
import RetryTimes from 'FEATURES/front_qe_tools/plan/components/Setting/RetryTimes';
import LocalDevice from 'FEATURES/front_qe_tools/plan/components/Setting/LocalDevice';
import EnvParams from 'FEATURES/front_qe_tools/plan/components/Setting/EnvParams';
import styles from './CreatePlanPage.module.less';

function CreatePlanPage(props) {
    const { currentSpace } = props;
    const query = getQueryParams();
    const [deviceType, setDeviceType] = useState('1');
    const [loading, setLoading] = useState(true);
    const [groupId, setGroupId] = useState(null);
    const [directoryTreeData, setDirectoryTreeData] = useState([]);
    const [stepInfoList, setStepInfoList] = useState([]);
    const [treeNodeId, setTreeNodeId] = useState(null);
    const [caseNode, setCaseNode] = useState(null);
    const [sceneListMap, setSceneListMap] = useState({});
    const [createLoading, setCreateLoading] = useState(false);
    const [groupList, setGroupList] = useState([]);
    const [envList, setEnvList] = useState([]); // 环境列表
    const [createPlanOption, setCreatePlanOption] = useState({
        planName: null,
        executeConfig: {
            android: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            ios: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            server: {
                deviceType: null,
                deviceId: null,
                envParams: {}
            }
        }
    });
    const [configForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate();

    const handleChangeDeviceType = (e) => {
        const newDeviceType = e.target.value;
        setDeviceType(newDeviceType);
        // 同步更新表单值
        configForm.setFieldsValue({
            deviceType: newDeviceType
        });
    };

    const handleChangeGroupId = (value) => {
        setGroupId(value);
        setTreeNodeId(null);
        setCaseNode(null);
        setStepInfoList([]);
        configForm.setFieldsValue({
            treeNodeId: undefined,
            caseNodeId: undefined
        });
    };

    const handleChangeTreeNodeId = (value) => {
        console.log('value-handleChangeTreeNodeId', value);
        console.log('directoryTreeData', directoryTreeData);
        setTreeNodeId(value);
        setCaseNode(null);
        setStepInfoList([]);
        configForm.setFieldsValue({
            caseNodeId: undefined
        });
    };

    const handleChangeCaseNodeId = (value) => {
        const stepInfo = getStepInfoByCaseNodeIds(caseNode, value);
        console.log('打印-stepInfo', stepInfo);
        setStepInfoList(stepInfo);
        setSceneListMap({});
    };

    // 添加场景
    const handleAddScene = (stepInfoIndex) => {
        const currentScenes = sceneListMap[stepInfoIndex] || [];
        const newScene = {
            id: Date.now(),
            name: `场景 - ${currentScenes.length + 1}`,
            firstFrame: '控件操作',
            lastFrame: '用例操作'
        };
        setSceneListMap({
            ...sceneListMap,
            [stepInfoIndex]: [...currentScenes, newScene]
        });
    };

    // 删除场景
    const handleDeleteScene = (stepInfoIndex, sceneId) => {
        const currentScenes = sceneListMap[stepInfoIndex] || [];
        setSceneListMap({
            ...sceneListMap,
            [stepInfoIndex]: currentScenes.filter((scene) => scene.id !== sceneId)
        });
    };

    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            // 获取目录树
            let { groupList } = await getGroupList({
                moduleId: currentSpace?.id,
                isArchive: 0
            });
            console.log('groupList', groupList);
            if (isEmpty(groupList)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setGroupList(groupList);
            // setGroupId(groupList[0]?.groupId);
        }
        func();
    }, [currentSpace?.id, query?.planType]);

    useEffect(() => {
        async function func() {
            if (!groupId) {
                return;
            }
            let { tree } = await getTreeNodeList({ groupId: groupId });
            if (isEmpty(tree)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setDirectoryTreeData(tree);
        }
        func();
    }, [groupId]);

    useEffect(() => {
        async function func() {
            if (!treeNodeId) {
                return;
            }
            const caseRootId = directoryTreeData.find(
                (item) => item?.nodeId === treeNodeId
            )?.caseRootId;
            if (!caseRootId) {
                return;
            }
            let res = await getCaseTree({ caseRootId: caseRootId, withSign: false });
            console.log('打印res', res);
            setCaseNode(res);
        }
        func();
    }, [treeNodeId]);

    useEffect(() => {
        obtaionEnvParamsList();
        // 初始化表单的设备类型值
        configForm.setFieldsValue({
            deviceType: '1'
        });
    }, []);

    // 监听表单值变化，同步更新 deviceType 状态
    useEffect(() => {
        const deviceTypeValue = configForm.getFieldValue('deviceType');
        if (deviceTypeValue && deviceTypeValue !== deviceType) {
            setDeviceType(deviceTypeValue);
        }
    }, [configForm]);

    // 获取运行环境列表
    const obtaionEnvParamsList = async () => {
        try {
            if (!currentSpace?.id) {
                return;
            }
            let androidRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 1
            });
            let iosRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 2
            });
            let serverRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 4
            });
            setEnvList({
                android: androidRes.envList,
                ios: iosRes.envList,
                server: serverRes?.envList
            });
        } catch (err) {
            message.error(err?.message ?? err);
        }
    };

    const handleCreatePlan = () => {
        configForm
            ?.validateFields()
            .then(async (values) => {
                let body = {
                    moduleId: currentSpace?.id,
                    name: values.name,
                    type: +values.type,
                    // 用例参数
                    caseNodeParams: {
                        treeNodeId: treeNodeId,
                        osType: +values.deviceType,
                        caseNodeList:
                            values.caseNodeId?.map((caseNodeId, index) => {
                                const scenes = sceneListMap[index] || [];
                                return {
                                    caseNodeId: caseNodeId,
                                    recordStartStrategy: 'case_0_start',
                                    sceneList: scenes.map((scene) => ({
                                        name: scene.name,
                                        firstFrameStrategy:
                                            scene.firstFrame === '控件操作'
                                                ? 'algorithm_0_crossdet'
                                                : 'steps_0_start',
                                        lastFrameStrategy:
                                            scene.lastFrame === '用例操作'
                                                ? 'case_0_end'
                                                : 'steps_0_end'
                                    }))
                                };
                            }) || []
                    },
                    // 执行参数
                    cloudParams: {
                        type: +values.deviceType,
                        deviceId:
                            +values.deviceType === 1
                                ? createPlanOption.executeConfig.android.deviceId
                                : createPlanOption.executeConfig.ios.deviceId,
                        executeTimes: +values.executeTimes,
                        envParams: +values.envId
                    }
                };

                console.log('创建计划的请求体:', body);
                setCreateLoading(true);
                createPerfPlanList(body)
                    .then((res) => {
                        messageApi.success('任务创建成功');
                        EventBus.emit('refreshPerfPlanList');
                        navigate(
                            stringifyUrl({
                                url: '/lazyperf/index',
                                query: {
                                    moduleId: currentSpace?.id,
                                    planId: 111,
                                    planType: 1,
                                    stage: 'task'
                                }
                            })
                        );
                        setCreateLoading(false);
                    })
                    .catch(() => {
                        setCreateLoading(false);
                    });
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setCreateLoading(false);
                messageApi.warning('请填写完整表单');
            });
    };

    const groupOptions = () => {
        console.log('group', groupList);
        return groupList.map((group) => ({
            value: group.groupId,
            key: group.groupId,
            label: group.groupName
        }));
    };

    const directoryTreeDataOptions = (tree = directoryTreeData) => {
        console.log('directoryTreeData', tree);

        let result = [];
        tree.forEach((item) => {
            if (item?.nodeType === 2) {
                result.push({
                    value: item?.nodeId,
                    key: item?.nodeId,
                    label: item?.nodeName
                });
            }
            if (Array.isArray(item?.children) && item.children.length) {
                result = result.concat(directoryTreeDataOptions(item.children));
            }
        });
        return result;
    };

    const caseNodeOptions = (tree = caseNode) => {
        if (!tree) {
            return [];
        }
        const arr = Array.isArray(tree) ? tree : [tree];
        let result = [];
        arr.forEach((item) => {
            if (item && item.nodeName && item.caseNodeId) {
                result.push({
                    value: item?.caseNodeId,
                    key: item?.caseNodeId,
                    label: item?.nodeName
                });
            }
            if (Array.isArray(item.children) && item.children.length > 0) {
                result = result.concat(caseNodeOptions(item.children));
            }
        });
        return result;
    };

    const getStepInfoByCaseNodeIds = (caseNode, caseNodeIds) => {
        const nodesArray = Array.isArray(caseNode) ? caseNode : [caseNode];
        const searchNodes = (nodes, ids) => {
            let result = [];
            for (const node of nodes) {
                if (ids.includes(node.caseNodeId)) {
                    result.push({
                        caseNodeId: node.caseNodeId,
                        caseNodeName: node.nodeName,
                        stepInfo: node.extra.stepInfo
                    });
                }
                if (!isEmpty(node.children)) {
                    result = result.concat(searchNodes(node.children, ids));
                }
            }

            return result;
        };
        return searchNodes(nodesArray, caseNodeIds);
    };

    console.log('stepInfoList', stepInfoList);

    return (
        <Spin spinning={createLoading}>
            <CardContent>
                {contextHolder}
                <Descriptions column={1} size="small" />
                <div className={styles.header}>
                    <CardHeader
                        text={'新建计划'}
                        extra={
                            <>
                                <Button type="primary" onClick={handleCreatePlan}>
                                    创建
                                </Button>
                                <Button
                                    onClick={() => {
                                        navigate(-1);
                                    }}
                                >
                                    取消
                                </Button>
                            </>
                        }
                    />
                </div>
                <div className={styles.container}>
                    <Form form={configForm} layout="horizontal" requiredMark={false} colon={false}>
                        <CardTitle text="任务配置" />
                        <Form.Item name="name" label="任务名称">
                            <Input placeholder="请输入任务名称" allowClear />
                        </Form.Item>
                        <Form.Item name="type" label="评测类型" initialValue={'1'.toString()}>
                            <Radio.Group>
                                <Radio value="1">速度评测</Radio>
                                <Radio value="2">指标评测</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <CardTitle text="设备配置" />
                        <Form.Item name="deviceType" label="设备类型" initialValue={'1'}>
                            <Radio.Group onChange={handleChangeDeviceType}>
                                <Radio value="1">Android</Radio>
                                <Radio value="2">iOS</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <LocalDevice form={configForm} osType={deviceType} />
                        <RetryTimes
                            form={configForm}
                            label="执行次数"
                            min={1}
                            max={99}
                            name="executeTimes"
                            initialValue={1}
                            required
                            tooltip="最大执行次数，范围：1-99"
                            placeholder="请设置执行次数"
                        />
                        <EnvParams
                            form={configForm}
                            osType={+deviceType}
                            envList={envList[[+deviceType === 2 ? 'ios' : 'android']]}
                            createPlanOption={createPlanOption}
                            setCreatePlanOption={setCreatePlanOption}
                        />
                        <CardTitle text="用例配置" />
                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '10px'
                            }}
                        >
                            <Form.Item label="执行用例" name="groupId">
                                <Select
                                    style={{ width: 150 }}
                                    className={styles.selecPlanType}
                                    options={groupOptions()}
                                    allowClear
                                    placeholder="请选择用例组"
                                    onChange={handleChangeGroupId}
                                />
                            </Form.Item>
                            <Form.Item name="treeNodeId">
                                <Select
                                    style={{ width: 150 }}
                                    className={styles.selecPlanType}
                                    options={directoryTreeDataOptions()}
                                    allowClear
                                    placeholder="请选择目录树"
                                    onChange={handleChangeTreeNodeId}
                                />
                            </Form.Item>
                            <Form.Item name="caseNodeId">
                                <Select
                                    mode="multiple"
                                    style={{ width: 250 }}
                                    className={styles.selecPlanType}
                                    options={caseNodeOptions()}
                                    allowClear
                                    placeholder="请选择执行用例"
                                    onChange={handleChangeCaseNodeId}
                                />
                            </Form.Item>
                        </div>

                        {/* 当选择执行用例后显示的场景配置 */}
                        {stepInfoList && stepInfoList.length > 0 && (
                            <>
                                {stepInfoList.map((stepInfo, index) => {
                                    console.log('stepInfo', stepInfo);
                                    return (
                                        <Card
                                            key={stepInfo.caseNodeId}
                                            title={`${stepInfo.caseNodeName || `用例${index + 1}`}`}
                                            size="small"
                                            style={{ marginBottom: '24px' }}
                                        >
                                            <div
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: '16px',
                                                    marginBottom: '16px'
                                                }}
                                            >
                                                <Tag
                                                    color="blue"
                                                    style={{
                                                        cursor: 'pointer'
                                                    }}
                                                    onClick={() => handleAddScene(index)}
                                                >
                                                    + 添加场景
                                                </Tag>
                                                <Form.Item label="录制起始" style={{ margin: 0 }}>
                                                    <Select
                                                        defaultValue="用例操作"
                                                        options={[
                                                            {
                                                                value: '用例操作',
                                                                label: '用例操作'
                                                            },
                                                            { value: '执行开始', label: '执行开始' }
                                                        ]}
                                                    />
                                                </Form.Item>
                                            </div>
                                            {(sceneListMap[index] || []).map((scene) => (
                                                <Card
                                                    key={scene.id}
                                                    size="small"
                                                    style={{ marginBottom: '12px' }}
                                                >
                                                    <Form.Item label="场景名称" name="sceneName">
                                                        <Input
                                                            placeholder={scene.name}
                                                            defaultValue={scene.name}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        label="首帧界定"
                                                        name="firstFrameStrategy"
                                                    >
                                                        <Select
                                                            className={styles.selecPlanType}
                                                            defaultValue={scene.firstFrame}
                                                            options={[
                                                                {
                                                                    value: '控件操作',
                                                                    label: '控件操作'
                                                                },
                                                                {
                                                                    value: '步骤2:刷新页面',
                                                                    label: '步骤2:刷新页面'
                                                                }
                                                            ]}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        label="尾帧界定"
                                                        name="lastFrameStrategy"
                                                    >
                                                        <Select
                                                            className={styles.selecPlanType}
                                                            defaultValue={scene.lastFrame}
                                                            options={[
                                                                {
                                                                    value: '用例操作',
                                                                    label: '用例操作'
                                                                },
                                                                {
                                                                    value: '执行结束',
                                                                    label: '执行结束'
                                                                }
                                                            ]}
                                                        />
                                                    </Form.Item>

                                                    <Tag
                                                        color="red"
                                                        style={{
                                                            cursor: 'pointer'
                                                        }}
                                                        onClick={() =>
                                                            handleDeleteScene(index, scene.id)
                                                        }
                                                    >
                                                        删除场景
                                                    </Tag>
                                                </Card>
                                            ))}
                                        </Card>
                                    );
                                })}
                            </>
                        )}
                    </Form>
                </div>
            </CardContent>
        </Spin>
    );
}

export default connectModel([baseModel, planModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentModule: state.common.base.currentModule
}))(CreatePlanPage);
